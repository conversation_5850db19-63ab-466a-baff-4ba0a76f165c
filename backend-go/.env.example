# ADC Multi-Languages Backend Configuration

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
PORT=8300
GIN_MODE=debug
BASE_URL=http://localhost:8300

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_HOST=localhost
DB_PORT=5432
DB_USER=adc_user
DB_PASSWORD=your_secure_password
DB_NAME=adc_multi_languages
DB_SSL_MODE=disable
DB_TIMEZONE=UTC

# =============================================================================
# JWT AUTHENTICATION
# =============================================================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRY=24h
JWT_REFRESH_EXPIRY=168h

# =============================================================================
# EMAIL CONFIGURATION (SMTP)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=ADC Multi-Languages

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
FRONTEND_URL=http://localhost:3300
SUPPORT_EMAIL=<EMAIL>

# =============================================================================
# AI TRANSLATION SERVICES
# =============================================================================

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key

# Google Translate Configuration
GOOGLE_TRANSLATE_API_KEY=your-google-translate-api-key

# Azure Translator Configuration
AZURE_TRANSLATOR_API_KEY=your-azure-translator-api-key
AZURE_TRANSLATOR_REGION=eastus
AZURE_TRANSLATOR_ENDPOINT=https://api.cognitive.microsofttranslator.com

# Default translation provider (openai, google, azure)
DEFAULT_TRANSLATION_PROVIDER=openai

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760

# =============================================================================
# STRIPE CONFIGURATION (for payments)
# =============================================================================
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
RATE_LIMIT_GENERAL=100
RATE_LIMIT_AUTH=10
RATE_LIMIT_API_KEY=1000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ALLOWED_ORIGINS=http://localhost:3300,http://localhost:3301
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Origin,Content-Type,Accept,Authorization,X-Requested-With

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_AI_TRANSLATION=true
ENABLE_FILE_UPLOAD=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_PASSWORD_RESET=true
ENABLE_WEBHOOKS=true
ENABLE_ADMIN_ROUTES=true
ENABLE_RATE_LIMITING=true
ENABLE_CACHING=true

# =============================================================================
# NOTES
# =============================================================================
# 1. Change all default passwords and secrets before deploying to production
# 2. Use environment-specific values for different deployment stages
# 3. Store sensitive values in a secure secret management system
# 4. Regularly rotate API keys and secrets
# 5. Enable HTTPS in production
