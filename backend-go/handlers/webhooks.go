package handlers

import (
	"encoding/json"
	"io"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/stripe/stripe-go/v76"
	"github.com/stripe/stripe-go/v76/webhook"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// WebhookHandler handles webhook-related requests
type WebhookHandler struct{}

// NewWebhookHandler creates a new webhook handler
func NewWebhookHandler() *WebhookHandler {
	return &WebhookHandler{}
}

// StripeWebhookEvent represents a Stripe webhook event
type StripeWebhookEvent struct {
	ID      string                 `json:"id"`
	Type    string                 `json:"type"`
	Data    map[string]interface{} `json:"data"`
	Created int64                  `json:"created"`
}

// StripeWebhook handles POST /api/webhooks/stripe
func (h *WebhookHandler) StripeWebhook(c *gin.Context) {
	// Read the request body
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		utils.BadRequest(c, "Failed to read request body")
		return
	}

	// Get the Stripe signature from the header
	signature := c.GetHeader("Stripe-Signature")
	if signature == "" {
		utils.BadRequest(c, "Missing Stripe-Signature header")
		return
	}

	// Get the webhook secret from environment
	webhookSecret := os.Getenv("STRIPE_WEBHOOK_SECRET")
	if webhookSecret == "" {
		utils.Logger.Warn("STRIPE_WEBHOOK_SECRET not set - webhook signature verification disabled")
		// Continue without verification in development
		if os.Getenv("GO_ENV") == "production" {
			utils.BadRequest(c, "Webhook signature verification required in production")
			return
		}
	}

	// Verify the webhook signature if secret is available
	var event stripe.Event
	if webhookSecret != "" {
		var err error
		event, err = webhook.ConstructEvent(body, signature, webhookSecret)
		if err != nil {
			utils.Logger.Error("Failed to verify webhook signature: " + err.Error())
			utils.BadRequest(c, "Invalid webhook signature")
			return
		}
	} else {
		// Parse without verification (development only)
		if err := json.Unmarshal(body, &event); err != nil {
			utils.BadRequest(c, "Invalid JSON payload")
			return
		}
	}

	// Handle different event types
	switch event.Type {
	case "customer.subscription.created":
		h.handleSubscriptionCreated(c, event)
	case "customer.subscription.updated":
		h.handleSubscriptionUpdated(c, event)
	case "customer.subscription.deleted":
		h.handleSubscriptionDeleted(c, event)
	case "invoice.payment_succeeded":
		h.handlePaymentSucceeded(c, event)
	case "invoice.payment_failed":
		h.handlePaymentFailed(c, event)
	default:
		// Log unhandled event types but return success
		utils.Logger.Info("Unhandled Stripe webhook event type: " + event.Type)
	}

	// Always return 200 OK to acknowledge receipt
	c.JSON(200, gin.H{"received": true})
}

// handleSubscriptionCreated handles subscription creation events
func (h *WebhookHandler) handleSubscriptionCreated(c *gin.Context, event stripe.Event) {
	utils.Logger.Info("Processing subscription created event: " + event.ID)

	// Extract subscription data
	subscription := event.Data.Object
	if subscription == nil {
		utils.Logger.Error("Invalid subscription data in webhook")
		return
	}

	customerID, ok := subscription["customer"].(string)
	if !ok {
		utils.Logger.Error("Missing customer ID in subscription webhook")
		return
	}

	// Find organization by Stripe customer ID
	db := database.GetDB()
	var organization models.Organization
	if err := db.Where("stripe_customer_id = ?", customerID).First(&organization).Error; err != nil {
		utils.Logger.Error("Organization not found for customer ID: " + customerID)
		return
	}

	// Update organization subscription status
	subscriptionID, _ := subscription["id"].(string)
	status, _ := subscription["status"].(string)

	organization.StripeSubscriptionID = &subscriptionID
	organization.SubscriptionStatus = &status

	if err := db.Save(&organization).Error; err != nil {
		utils.Logger.Error("Failed to update organization subscription: " + err.Error())
		return
	}

	utils.Logger.Info("Successfully processed subscription created for organization: " + organization.ID.String())
}

// handleSubscriptionUpdated handles subscription update events
func (h *WebhookHandler) handleSubscriptionUpdated(c *gin.Context, event stripe.Event) {
	utils.Logger.Info("Processing subscription updated event: " + event.ID)

	// Extract subscription data
	subscription := event.Data.Object
	if subscription == nil {
		utils.Logger.Error("Invalid subscription data in webhook")
		return
	}

	subscriptionID, ok := subscription["id"].(string)
	if !ok {
		utils.Logger.Error("Missing subscription ID in webhook")
		return
	}

	// Find organization by Stripe subscription ID
	db := database.GetDB()
	var organization models.Organization
	if err := db.Where("stripe_subscription_id = ?", subscriptionID).First(&organization).Error; err != nil {
		utils.Logger.Error("Organization not found for subscription ID: " + subscriptionID)
		return
	}

	// Update subscription status
	status, _ := subscription["status"].(string)
	organization.SubscriptionStatus = &status

	if err := db.Save(&organization).Error; err != nil {
		utils.Logger.Error("Failed to update organization subscription: " + err.Error())
		return
	}

	utils.Logger.Info("Successfully processed subscription updated for organization: " + organization.ID.String())
}

// handleSubscriptionDeleted handles subscription deletion events
func (h *WebhookHandler) handleSubscriptionDeleted(c *gin.Context, event stripe.Event) {
	utils.Logger.Info("Processing subscription deleted event: " + event.ID)

	// Extract subscription data
	subscription := event.Data.Object
	if subscription == nil {
		utils.Logger.Error("Invalid subscription data in webhook")
		return
	}

	subscriptionID, ok := subscription["id"].(string)
	if !ok {
		utils.Logger.Error("Missing subscription ID in webhook")
		return
	}

	// Find organization by Stripe subscription ID
	db := database.GetDB()
	var organization models.Organization
	if err := db.Where("stripe_subscription_id = ?", subscriptionID).First(&organization).Error; err != nil {
		utils.Logger.Error("Organization not found for subscription ID: " + subscriptionID)
		return
	}

	// Update subscription status to canceled and reset to free tier
	canceledStatus := "canceled"
	organization.SubscriptionStatus = &canceledStatus
	organization.SubscriptionTier = "free"
	organization.StripeSubscriptionID = nil

	if err := db.Save(&organization).Error; err != nil {
		utils.Logger.Error("Failed to update organization subscription: " + err.Error())
		return
	}

	utils.Logger.Info("Successfully processed subscription deleted for organization: " + organization.ID.String())
}

// handlePaymentSucceeded handles successful payment events
func (h *WebhookHandler) handlePaymentSucceeded(c *gin.Context, event stripe.Event) {
	utils.Logger.Info("Processing payment succeeded event: " + event.ID)

	// Extract invoice data
	invoice := event.Data.Object
	if invoice == nil {
		utils.Logger.Error("Invalid invoice data in webhook")
		return
	}

	customerID, ok := invoice["customer"].(string)
	if !ok {
		utils.Logger.Error("Missing customer ID in invoice webhook")
		return
	}

	// Find organization by Stripe customer ID
	db := database.GetDB()
	var organization models.Organization
	if err := db.Where("stripe_customer_id = ?", customerID).First(&organization).Error; err != nil {
		utils.Logger.Error("Organization not found for customer ID: " + customerID)
		return
	}

	// Update subscription status to active if it was past_due
	if organization.SubscriptionStatus != nil && *organization.SubscriptionStatus == "past_due" {
		activeStatus := "active"
		organization.SubscriptionStatus = &activeStatus

		if err := db.Save(&organization).Error; err != nil {
			utils.Logger.Error("Failed to update organization subscription: " + err.Error())
			return
		}
	}

	utils.Logger.Info("Successfully processed payment succeeded for organization: " + organization.ID.String())
}

// handlePaymentFailed handles failed payment events
func (h *WebhookHandler) handlePaymentFailed(c *gin.Context, event stripe.Event) {
	utils.Logger.Info("Processing payment failed event: " + event.ID)

	// Extract invoice data
	invoice := event.Data.Object
	if invoice == nil {
		utils.Logger.Error("Invalid invoice data in webhook")
		return
	}

	customerID, ok := invoice["customer"].(string)
	if !ok {
		utils.Logger.Error("Missing customer ID in invoice webhook")
		return
	}

	// Find organization by Stripe customer ID
	db := database.GetDB()
	var organization models.Organization
	if err := db.Where("stripe_customer_id = ?", customerID).First(&organization).Error; err != nil {
		utils.Logger.Error("Organization not found for customer ID: " + customerID)
		return
	}

	// Update subscription status to past_due
	pastDueStatus := "past_due"
	organization.SubscriptionStatus = &pastDueStatus

	if err := db.Save(&organization).Error; err != nil {
		utils.Logger.Error("Failed to update organization subscription: " + err.Error())
		return
	}

	utils.Logger.Info("Successfully processed payment failed for organization: " + organization.ID.String())
}
