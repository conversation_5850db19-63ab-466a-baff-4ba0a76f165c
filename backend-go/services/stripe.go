package services

import (
	"adc-multi-languages/config"
	"adc-multi-languages/utils"

	"github.com/stripe/stripe-go/v76"
)

// InitializeStripe initializes the Stripe SDK with the secret key
func InitializeStripe(cfg *config.Config) {
	if cfg.StripeSecretKey == "" {
		utils.Logger.Warn("STRIPE_SECRET_KEY not set - Stripe functionality will be disabled")
		return
	}

	// Set the Stripe API key
	stripe.Key = cfg.StripeSecretKey

	utils.Logger.Info("Stripe SDK initialized successfully")
}

// IsStripeConfigured returns true if Stripe is properly configured
func IsStripeConfigured() bool {
	return stripe.Key != ""
}
