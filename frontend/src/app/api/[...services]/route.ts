import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Backend API URL - using port 8300 for Go backend
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8300';

// Public endpoints that don't require authentication
const PUBLIC_ENDPOINTS = [
  '/health',
  '/api/health',
  '/api/public',
  '/api/auth/signin',
  '/api/auth/signup',
  '/api/auth/forgot-password',
  '/api/auth/reset-password',
  '/api/locales',
  '/api/subscription-plans',
  '/api/ai-credits/pricing',
  '/api/webhooks/stripe',
];

// File upload endpoints that need special handling
const UPLOAD_ENDPOINTS = [
  '/api/storage/upload',
  '/api/users/avatar',
  '/api/organizations/logo',
];

/**
 * Universal API proxy handler
 * This catches all /api/* requests and forwards them to the backend
 */
async function handleRequest(request: NextRequest, method: string) {
  const startTime = Date.now();
  
  try {
    // Extract the service path from the URL
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/').filter(Boolean);
    
    // Remove 'api' from the path to get the service path
    const serviceIndex = pathSegments.indexOf('api');
    if (serviceIndex === -1) {
      return NextResponse.json(
        { success: false, message: 'Invalid API path' },
        { status: 400 }
      );
    }
    
    // Reconstruct the backend path
    const servicePath = '/' + pathSegments.slice(serviceIndex).join('/');
    
    // Log the request in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[API PROXY] ${method} ${servicePath}`);
    }
    
    // Check if this is a public endpoint
    const isPublicEndpoint = PUBLIC_ENDPOINTS.some(endpoint => 
      servicePath.startsWith(endpoint)
    );
    
    // Check if this is an upload endpoint
    const isUploadEndpoint = UPLOAD_ENDPOINTS.some(endpoint => 
      servicePath.startsWith(endpoint)
    );
    
    // Get authentication if required
    let authHeaders: Record<string, string> = {};

    if (!isPublicEndpoint) {
      const session = await getServerSession(authOptions);

      // Debug logging
      if (process.env.NODE_ENV === 'development') {
        console.log('[AUTH DEBUG] Session:', {
          hasSession: !!session,
          hasAccessToken: !!session?.accessToken,
          accessTokenLength: session?.accessToken?.length,
          accessTokenStart: session?.accessToken?.substring(0, 20) + '...',
          user: session?.user?.email
        });
      }

      if (!session?.accessToken) {
        return NextResponse.json(
          {
            success: false,
            message: 'Unauthorized - Authentication required',
            data: null,
          },
          { status: 401 }
        );
      }

      authHeaders = { 'Authorization': `Bearer ${session.accessToken}` };
    }
    
    // Build the backend URL
    const backendUrl = new URL(servicePath, BACKEND_API_URL);
    
    // Copy query parameters
    url.searchParams.forEach((value, key) => {
      backendUrl.searchParams.append(key, value);
    });
    
    // Prepare headers
    const contentType = request.headers.get('content-type');
    const headers: Record<string, string> = {
      ...authHeaders,
    };
    
    // Handle different content types
    if (!isUploadEndpoint && !contentType?.includes('multipart/form-data')) {
      headers['Content-Type'] = 'application/json';
    }
    
    // Forward important headers
    const headersToForward = [
      'user-agent', 'accept', 'accept-language', 'x-forwarded-for',
      'x-real-ip', 'x-requested-with'
    ];
    
    headersToForward.forEach(headerName => {
      const value = request.headers.get(headerName);
      if (value && !headers[headerName]) {
        headers[headerName] = value;
      }
    });
    
    // Prepare fetch options
    const fetchOptions: RequestInit = {
      method,
      headers,
      signal: AbortSignal.timeout(isUploadEndpoint ? 60000 : 30000), // 60s for uploads, 30s for others
    };
    
    // Add body for non-GET requests
    if (method !== 'GET' && method !== 'HEAD') {
      try {
        if (contentType?.includes('multipart/form-data')) {
          // Handle file uploads
          const formData = await request.formData();
          fetchOptions.body = formData;
          // Remove Content-Type to let fetch set it with boundary
          delete headers['Content-Type'];
        } else if (contentType?.includes('application/json')) {
          // Handle JSON data
          const body = await request.json();
          fetchOptions.body = JSON.stringify(body);
        } else {
          // Handle other data types
          const body = await request.text();
          if (body) {
            fetchOptions.body = body;
          }
        }
      } catch (error) {
        console.error('Error processing request body:', error);
        return NextResponse.json(
          {
            success: false,
            message: 'Invalid request body',
            data: null,
          },
          { status: 400 }
        );
      }
    }
    
    // Make the request to the backend
    const response = await fetch(backendUrl.toString(), fetchOptions);
    const duration = Date.now() - startTime;
    
    // Log the response in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[API PROXY] ${method} ${servicePath} -> ${response.status} (${duration}ms)`);
    }
    
    // Handle the response
    const responseContentType = response.headers.get('content-type');
    
    if (responseContentType?.includes('application/json')) {
      try {
        const data = await response.json();
        const nextResponse = NextResponse.json(data, { status: response.status });
        
        // Forward important response headers
        const responseHeadersToForward = [
          'x-total-count', 'x-page', 'x-per-page', 'x-total-pages',
          'cache-control', 'etag', 'last-modified', 'content-disposition'
        ];
        
        responseHeadersToForward.forEach(header => {
          const value = response.headers.get(header);
          if (value) {
            nextResponse.headers.set(header, value);
          }
        });
        
        return nextResponse;
      } catch {
        // If JSON parsing fails, treat as text
        const text = await response.text();
        return new NextResponse(text, {
          status: response.status,
          headers: { 'Content-Type': 'text/plain' },
        });
      }
    } else {
      // Handle non-JSON responses (files, images, etc.)
      const blob = await response.blob();
      return new NextResponse(blob, {
        status: response.status,
        headers: {
          'Content-Type': responseContentType || 'application/octet-stream',
          'Content-Length': blob.size.toString(),
        },
      });
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error('Universal proxy error:', {
      method,
      url: request.url,
      error: error instanceof Error ? error.message : String(error),
      duration,
    });
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        return NextResponse.json(
          {
            success: false,
            message: 'Request timeout - backend service took too long to respond',
            data: null,
          },
          { status: 504 }
        );
      }
      
      if (error.message.includes('fetch') || error.message.includes('ECONNREFUSED')) {
        return NextResponse.json(
          {
            success: false,
            message: 'Unable to connect to backend service - please check if the backend is running on port 8300',
            data: null,
          },
          { status: 503 }
        );
      }
    }
    
    return NextResponse.json(
      {
        success: false,
        message: 'An unexpected error occurred while processing the request',
        data: null,
        error: process.env.NODE_ENV === 'development' ? String(error) : undefined,
      },
      { status: 500 }
    );
  }
}

// Export HTTP method handlers
export async function GET(request: NextRequest) {
  return handleRequest(request, 'GET');
}

export async function POST(request: NextRequest) {
  return handleRequest(request, 'POST');
}

export async function PUT(request: NextRequest) {
  return handleRequest(request, 'PUT');
}

export async function DELETE(request: NextRequest) {
  return handleRequest(request, 'DELETE');
}

export async function PATCH(request: NextRequest) {
  return handleRequest(request, 'PATCH');
}

export async function OPTIONS(request: NextRequest) {
  // Handle CORS preflight requests
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
      'Access-Control-Max-Age': '86400',
    },
  });
}
