'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  useGetOrganizationBySlugQuery,
  useUpdateOrganizationMutation,
  useGetAICreditsQuery,
  useListProjectsQuery,
  useListProjectLocalesQuery,
  useListOrganizationMembersQuery,
  useInviteOrganizationMemberMutation,
  useRemoveOrganizationMemberMutation,
  useUpdateOrganizationMemberRoleMutation,
  useGetOrganizationSubscriptionQuery,
  useListOrganizationPaymentMethodsQuery,
  useAddOrganizationPaymentMethodMutation,
  useRemoveOrganizationPaymentMethodMutation,
  useSetDefaultOrganizationPaymentMethodMutation,
  useCreateOrganizationSubscriptionMutation,
  useCancelOrganizationSubscriptionMutation,
} from '@/lib/redux/api/endpoints';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  ArrowLeft,
  Building,
  Users,
  Calendar,
  CreditCard,
  Zap,
  Plus,
  FileText,
  Globe,
  Loader2,
  Key,
  Settings,
} from 'lucide-react';
import { formatDate } from '@/lib/utils';

// ProjectLocales component to display locales for a project
function ProjectLocales({ projectId }: { projectId: string }) {
  const { data: localesData, isLoading, error } = useListProjectLocalesQuery(projectId, {
    skip: !projectId
  });

  if (isLoading) {
    return <span>Loading locales...</span>;
  }

  if (error) {
    return <span>Error loading locales</span>;
  }

  if (!localesData?.data || localesData.data.length === 0) {
    return <span>No locales configured</span>;
  }

  const localeCount = localesData.data.length;
  const sourceLocale = localesData.data.find(locale => locale.is_source);

  return (
    <div className="flex items-center">
      <span className="inline-flex items-center rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium text-primary mr-2">
        {localeCount} {localeCount === 1 ? 'locale' : 'locales'}
      </span>
      {sourceLocale && sourceLocale.locale_code && (
        <span className="text-xs">
          Source: {sourceLocale.locale_code}
        </span>
      )}
    </div>
  );
}

export default function OrganizationDetailPage({ params }: { params: { slug: string } }) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  // Create local variable to avoid direct access to params properties
  // This approach works with the current version of Next.js and avoids the warning
  const orgSlug = params?.slug;

  // Fetch organization by slug
  const {
    data: organizationData,
    isLoading: isLoadingOrg,
    error: orgError,
    refetch: refetchOrg,
  } = useGetOrganizationBySlugQuery(orgSlug);

  // Fetch AI credits if organization data is available
  const {
    data: creditsData,
    isLoading: isLoadingCredits,
    error: creditsError,
  } = useGetAICreditsQuery(
    organizationData?.data?.id || '',
    { skip: !organizationData?.data?.id }
  );

  // Fetch projects for the organization
  const {
    data: projectsData,
    isLoading: isLoadingProjects,
    error: projectsError,
    refetch: refetchProjects,
  } = useListProjectsQuery(
    { organization_id: organizationData?.data?.id || '' },
    { skip: !organizationData?.data?.id }
  );

  // Fetch organization members
  const {
    data: membersData,
    isLoading: isLoadingMembers,
    error: membersError,
    refetch: refetchMembers,
  } = useListOrganizationMembersQuery(
    organizationData?.data?.id || '',
    { skip: !organizationData?.data?.id }
  );

  // Member mutations
  const [inviteMember, { isLoading: isInviting }] = useInviteOrganizationMemberMutation();
  const [removeMember, { isLoading: isRemoving }] = useRemoveOrganizationMemberMutation();
  const [updateMemberRole] = useUpdateOrganizationMemberRoleMutation();

  // Fetch subscription data
  const {
    data: subscriptionData,
    isLoading: isLoadingSubscription,
    error: subscriptionError,
    refetch: refetchSubscription,
  } = useGetOrganizationSubscriptionQuery(
    organizationData?.data?.id || '',
    { skip: !organizationData?.data?.id }
  );

  // Fetch payment methods
  const {
    data: paymentMethodsData,
    isLoading: isLoadingPaymentMethods,
    error: paymentMethodsError,
    refetch: refetchPaymentMethods,
  } = useListOrganizationPaymentMethodsQuery(
    organizationData?.data?.id || '',
    { skip: !organizationData?.data?.id }
  );

  // Billing mutations
  const [addPaymentMethod, { isLoading: isAddingPayment }] = useAddOrganizationPaymentMethodMutation();
  const [removePaymentMethod, { isLoading: isRemovingPayment }] = useRemoveOrganizationPaymentMethodMutation();
  const [setDefaultPaymentMethod, { isLoading: isSettingDefaultPayment }] = useSetDefaultOrganizationPaymentMethodMutation();
  const [createSubscription, { isLoading: isCreatingSubscription }] = useCreateOrganizationSubscriptionMutation();
  const [cancelSubscription, { isLoading: isCancellingSubscription }] = useCancelOrganizationSubscriptionMutation();

  // Update organization mutation
  const [updateOrganization, { isLoading: isUpdating }] = useUpdateOrganizationMutation();

  // Handle back button click
  const handleBackClick = () => {
    router.push('/organizations');
  };

  // Loading state
  if (isLoadingOrg) {
    return (
      <div className="container mx-auto p-4 md:p-8 w-full">
        <div className="flex items-center mb-6">
          <Skeleton className="h-10 w-10 mr-4" />
          <Skeleton className="h-10 w-48" />
        </div>
        <Skeleton className="h-12 w-full mb-6" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    );
  }

  // Error state
  if (orgError) {
    return (
      <div className="container mx-auto p-4 md:p-8 w-full">
        <Button variant="outline" onClick={handleBackClick} className="mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Organizations
        </Button>

        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>Failed to load organization</CardDescription>
          </CardHeader>
          <CardContent>
            <p>There was an error loading the organization details. Please try again later.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => refetchOrg()}>Retry</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  const organization = organizationData?.data;

  if (!organization) {
    return (
      <div className="container mx-auto p-4 md:p-8 w-full">
        <Button variant="outline" onClick={handleBackClick} className="mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Organizations
        </Button>

        <Card>
          <CardHeader>
            <CardTitle>Organization Not Found</CardTitle>
            <CardDescription>The organization you're looking for doesn't exist or you don't have access to it.</CardDescription>
          </CardHeader>
          <CardContent>
            <p>Please check the organization slug or go back to the organizations list.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={handleBackClick}>
              Go Back
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-8 w-full">
      <div className="flex items-center mb-6">
        <Button variant="outline" onClick={handleBackClick} className="mr-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <h1 className="text-3xl font-bold flex items-center">
          <Building className="mr-3 h-6 w-6 text-primary" />
          {organization.name}
        </h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building className="mr-2 h-5 w-5 text-primary" />
                  Organization Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm font-medium">Name:</p>
                  <p>{organization.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Slug:</p>
                  <p>{organization.slug}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Created:</p>
                  <p>{organization.created_at ? formatDate(organization.created_at) : 'Unknown'}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="mr-2 h-5 w-5 text-primary" />
                  Subscription
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm font-medium">Plan:</p>
                  <p className="capitalize">{organization.subscription_tier || 'Free'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Status:</p>
                  <p className="capitalize">{organization.subscription_status || 'Active'}</p>
                </div>
                {organization.billing_period_end && (
                  <div>
                    <p className="text-sm font-medium">Next Billing Date:</p>
                    <p>{formatDate(organization.billing_period_end)}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* AI Credits Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Zap className="mr-2 h-5 w-5 text-primary" />
                  AI Credits
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoadingCredits ? (
                  <div className="space-y-4">
                    <Skeleton className="h-6 w-full" />
                    <Skeleton className="h-6 w-full" />
                    <Skeleton className="h-6 w-full" />
                  </div>
                ) : creditsError ? (
                  <p className="text-destructive">Failed to load AI credits information</p>
                ) : creditsData?.data ? (
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium">Available Credits:</p>
                      <p>{creditsData.data.remaining} / {creditsData.data.monthly_allowance}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Used Credits:</p>
                      <p>{creditsData.data.used}</p>
                    </div>
                    {creditsData.data.reset_date && (
                      <div>
                        <p className="text-sm font-medium">Reset Date:</p>
                        <p>{formatDate(creditsData.data.reset_date)}</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <p>No AI credits information available</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="projects">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-2xl font-bold">Projects</h2>
              <p className="text-muted-foreground">Manage your translation projects</p>
            </div>
            <Button onClick={() => router.push(`/organizations/${organization.slug}/projects/new`)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Project
            </Button>
          </div>

          {isLoadingProjects ? (
            <div className="space-y-4">
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
            </div>
          ) : projectsError ? (
            <Card>
              <CardHeader>
                <CardTitle>Error</CardTitle>
                <CardDescription>Failed to load projects</CardDescription>
              </CardHeader>
              <CardContent>
                <p>There was an error loading your projects. Please try again.</p>
              </CardContent>
              <CardFooter>
                <Button onClick={() => refetchProjects()}>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Retry
                </Button>
              </CardFooter>
            </Card>
          ) : projectsData?.data?.length === 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>No Projects</CardTitle>
                <CardDescription>You don't have any projects in this organization yet</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Create your first project to get started with translations.</p>
              </CardContent>
              <CardFooter>
                <Button onClick={() => router.push(`/organizations/${organization.slug}/projects/new`)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Project
                </Button>
              </CardFooter>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {projectsData?.data?.map((project) => (
                <Card
                  key={project.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => router.push(`/organizations/${organization.slug}/projects/${project.slug}`)}
                >
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <FileText className="mr-2 h-5 w-5 text-primary" />
                      {project.name}
                    </CardTitle>
                    <CardDescription>{project.slug}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center text-sm text-muted-foreground mb-2">
                      <Globe className="mr-2 h-4 w-4" />
                      <ProjectLocales projectId={project.id} />
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Calendar className="mr-2 h-4 w-4" />
                      Created: {project.created_at ? formatDate(project.created_at) : 'Unknown'}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="members">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-2xl font-bold">Members</h2>
              <p className="text-muted-foreground">Manage organization members and their roles</p>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Users className="mr-2 h-4 w-4" />
                  Invite Member
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Invite Member</DialogTitle>
                  <DialogDescription>
                    Invite a new member to join this organization.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={(e) => {
                  e.preventDefault();
                  const formData = new FormData(e.currentTarget);
                  const email = formData.get('email') as string;
                  const role = formData.get('role') as string;

                  if (email && role && organization.id) {
                    inviteMember({
                      organizationId: organization.id,
                      data: { email, role }
                    })
                      .unwrap()
                      .then(() => {
                        refetchMembers();
                        // Close dialog by clicking the close button
                        document.querySelector('[data-dialog-close]')?.dispatchEvent(
                          new MouseEvent('click', { bubbles: true })
                        );
                      })
                      .catch((err) => {
                        console.error('Failed to invite member:', err);
                      });
                  }
                }}>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="role">Role</Label>
                      <Select name="role" defaultValue="member">
                        <SelectTrigger>
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="admin">Admin</SelectItem>
                          <SelectItem value="member">Member</SelectItem>
                          <SelectItem value="viewer">Viewer</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="submit" disabled={isInviting}>
                      {isInviting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Inviting...
                        </>
                      ) : (
                        'Send Invitation'
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>

          {isLoadingMembers ? (
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
          ) : membersError ? (
            <Card>
              <CardHeader>
                <CardTitle>Error</CardTitle>
                <CardDescription>Failed to load members</CardDescription>
              </CardHeader>
              <CardContent>
                <p>There was an error loading the organization members. Please try again.</p>
              </CardContent>
              <CardFooter>
                <Button onClick={() => refetchMembers()}>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Retry
                </Button>
              </CardFooter>
            </Card>
          ) : membersData?.data?.length === 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>No Members</CardTitle>
                <CardDescription>This organization doesn't have any members yet</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Invite members to collaborate on your translation projects.</p>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Joined</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {membersData?.data?.map((member) => (
                      <TableRow key={member.id}>
                        <TableCell className="font-medium">
                          {member.user?.email || 'Unknown'}
                          {member.user?.full_name && (
                            <div className="text-xs text-muted-foreground">{member.user.full_name}</div>
                          )}
                        </TableCell>
                        <TableCell>
                          <Select
                            defaultValue={member.role}
                            onValueChange={(newRole) => {
                              if (organization.id && member.id) {
                                updateMemberRole({
                                  organizationId: organization.id,
                                  memberId: member.id,
                                  data: { role: newRole }
                                });
                              }
                            }}
                            disabled={member.user_id === organization.owner_id}
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="admin">Admin</SelectItem>
                              <SelectItem value="member">Member</SelectItem>
                              <SelectItem value="viewer">Viewer</SelectItem>
                            </SelectContent>
                          </Select>
                        </TableCell>
                        <TableCell>
                          {member.is_active ? (
                            <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                              Active
                            </span>
                          ) : (
                            <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                              Pending
                            </span>
                          )}
                        </TableCell>
                        <TableCell>
                          {member.joined_at ? formatDate(member.joined_at) : 'Not joined yet'}
                        </TableCell>
                        <TableCell className="text-right">
                          {member.user_id !== organization.owner_id && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                if (confirm('Are you sure you want to remove this member?')) {
                                  if (organization.id && member.id) {
                                    removeMember({
                                      organizationId: organization.id,
                                      memberId: member.id
                                    });
                                  }
                                }
                              }}
                              disabled={isRemoving}
                            >
                              {isRemoving ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                'Remove'
                              )}
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="billing">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-2xl font-bold">Billing</h2>
              <p className="text-muted-foreground">Manage your subscription and payment methods</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Subscription Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="mr-2 h-5 w-5 text-primary" />
                  Subscription
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {isLoadingSubscription ? (
                  <div className="space-y-4">
                    <Skeleton className="h-6 w-full" />
                    <Skeleton className="h-6 w-full" />
                    <Skeleton className="h-6 w-full" />
                  </div>
                ) : subscriptionError ? (
                  <div>
                    <p className="text-destructive">Failed to load subscription information</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => refetchSubscription()}
                    >
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Retry
                    </Button>
                  </div>
                ) : subscriptionData?.data ? (
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium">Plan:</p>
                      <p className="capitalize">{subscriptionData.data.plan_id}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Status:</p>
                      <p className="capitalize">{subscriptionData.data.status}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Current Period:</p>
                      <p>
                        {formatDate(subscriptionData.data.current_period_start)} - {formatDate(subscriptionData.data.current_period_end)}
                      </p>
                    </div>
                    {subscriptionData.data.cancel_at_period_end && (
                      <div className="p-3 bg-amber-50 text-amber-800 rounded-md text-sm">
                        Your subscription will end at the end of the current billing period.
                      </div>
                    )}
                    <div className="flex justify-between pt-2">
                      <Button
                        variant="outline"
                        onClick={() => router.push('/subscription')}
                      >
                        Change Plan
                      </Button>
                      {!subscriptionData.data.cancel_at_period_end ? (
                        <Button
                          variant="destructive"
                          onClick={() => {
                            if (confirm('Are you sure you want to cancel your subscription?')) {
                              if (organization.id) {
                                cancelSubscription({
                                  organizationId: organization.id,
                                  cancelAtPeriodEnd: true
                                });
                              }
                            }
                          }}
                          disabled={isCancellingSubscription}
                        >
                          {isCancellingSubscription ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            'Cancel Subscription'
                          )}
                        </Button>
                      ) : (
                        <Button
                          onClick={() => {
                            if (organization.id) {
                              cancelSubscription({
                                organizationId: organization.id,
                                cancelAtPeriodEnd: false
                              });
                            }
                          }}
                          disabled={isCancellingSubscription}
                        >
                          {isCancellingSubscription ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            'Resume Subscription'
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <p>You don't have an active subscription.</p>
                    <div className="p-3 bg-blue-50 text-blue-800 rounded-md text-sm">
                      Subscribe to a plan to unlock premium features and increase your AI translation credits.
                    </div>
                    <Button
                      onClick={() => router.push('/subscription')}
                      className="w-full"
                    >
                      View Subscription Plans
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Payment Methods Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="mr-2 h-5 w-5 text-primary" />
                  Payment Methods
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {isLoadingPaymentMethods ? (
                  <div className="space-y-4">
                    <Skeleton className="h-6 w-full" />
                    <Skeleton className="h-6 w-full" />
                    <Skeleton className="h-6 w-full" />
                  </div>
                ) : paymentMethodsError ? (
                  <div>
                    <p className="text-destructive">Failed to load payment methods</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => refetchPaymentMethods()}
                    >
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Retry
                    </Button>
                  </div>
                ) : paymentMethodsData?.data?.length === 0 ? (
                  <div className="space-y-4">
                    <p>You don't have any payment methods added yet.</p>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button>
                          <Plus className="mr-2 h-4 w-4" />
                          Add Payment Method
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add Payment Method</DialogTitle>
                          <DialogDescription>
                            Add a new payment method to your organization.
                          </DialogDescription>
                        </DialogHeader>
                        <form onSubmit={(e) => {
                          e.preventDefault();
                          const formData = new FormData(e.currentTarget);
                          const paymentMethodId = formData.get('paymentMethodId') as string;
                          const setAsDefault = formData.get('setAsDefault') === 'true';

                          if (paymentMethodId && organization.id) {
                            addPaymentMethod({
                              organizationId: organization.id,
                              paymentMethodId,
                              setAsDefault
                            })
                              .unwrap()
                              .then(() => {
                                refetchPaymentMethods();
                                // Close dialog by clicking the close button
                                document.querySelector('[data-dialog-close]')?.dispatchEvent(
                                  new MouseEvent('click', { bubbles: true })
                                );
                              })
                              .catch((err) => {
                                console.error('Failed to add payment method:', err);
                              });
                          }
                        }}>
                          <div className="grid gap-4 py-4">
                            <div className="grid gap-2">
                              <Label htmlFor="paymentMethodId">Payment Method ID</Label>
                              <Input
                                id="paymentMethodId"
                                name="paymentMethodId"
                                placeholder="pm_..."
                                required
                              />
                              <p className="text-xs text-muted-foreground">
                                Enter the Stripe Payment Method ID
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                id="setAsDefault"
                                name="setAsDefault"
                                value="true"
                                className="h-4 w-4 rounded border-gray-300"
                              />
                              <Label htmlFor="setAsDefault">Set as default payment method</Label>
                            </div>
                          </div>
                          <DialogFooter>
                            <Button type="submit" disabled={isAddingPayment}>
                              {isAddingPayment ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Adding...
                                </>
                              ) : (
                                'Add Payment Method'
                              )}
                            </Button>
                          </DialogFooter>
                        </form>
                      </DialogContent>
                    </Dialog>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {paymentMethodsData?.data?.map((method) => (
                      <div key={method.id} className="flex justify-between items-center p-3 border rounded-md">
                        <div>
                          <div className="font-medium">
                            {method.card_brand} •••• {method.card_last4}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Expires {method.card_exp_month}/{method.card_exp_year}
                            {method.is_default && (
                              <span className="ml-2 inline-flex items-center rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium text-primary">
                                Default
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          {!method.is_default && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                if (organization.id) {
                                  setDefaultPaymentMethod({
                                    organizationId: organization.id,
                                    paymentMethodId: method.id
                                  });
                                }
                              }}
                              disabled={isSettingDefaultPayment}
                            >
                              {isSettingDefaultPayment ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                'Set Default'
                              )}
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              if (confirm('Are you sure you want to remove this payment method?')) {
                                if (organization.id) {
                                  removePaymentMethod({
                                    organizationId: organization.id,
                                    paymentMethodId: method.id
                                  });
                                }
                              }
                            }}
                            disabled={isRemovingPayment}
                          >
                            {isRemovingPayment ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              'Remove'
                            )}
                          </Button>
                        </div>
                      </div>
                    ))}
                    <Button
                      variant="outline"
                      onClick={() => {
                        // Open add payment method dialog
                        document.querySelector('[data-dialog-trigger="add-payment"]')?.dispatchEvent(
                          new MouseEvent('click', { bubbles: true })
                        );
                      }}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Add Payment Method
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="api-keys">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-2xl font-bold">API Keys</h2>
              <p className="text-muted-foreground">Manage API keys for programmatic access</p>
            </div>
            <Button onClick={() => router.push(`/organizations/${organization.slug}/api-keys`)}>
              <Key className="mr-2 h-4 w-4" />
              Manage API Keys
            </Button>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>API Access</CardTitle>
              <CardDescription>Create and manage API keys for programmatic access to the API</CardDescription>
            </CardHeader>
            <CardContent>
              <p>API keys allow you to authenticate requests to the API. You can create multiple API keys with different permissions for different applications.</p>
            </CardContent>
            <CardFooter>
              <Button onClick={() => router.push(`/organizations/${organization.slug}/api-keys`)}>
                <Key className="mr-2 h-4 w-4" />
                Manage API Keys
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-2xl font-bold">Settings</h2>
              <p className="text-muted-foreground">Manage organization settings</p>
            </div>
            <Button onClick={() => router.push(`/organizations/${organization.slug}/settings`)}>
              <Settings className="mr-2 h-4 w-4" />
              Advanced Settings
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Organization Details Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building className="mr-2 h-5 w-5 text-primary" />
                  Organization Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={(e) => {
                  e.preventDefault();
                  const formData = new FormData(e.currentTarget);
                  const name = formData.get('name') as string;

                  if (name && organization.id) {
                    updateOrganization({
                      id: organization.id,
                      data: { name }
                    })
                      .unwrap()
                      .then(() => {
                        refetchOrg();
                      })
                      .catch((err) => {
                        console.error('Failed to update organization:', err);
                      });
                  }
                }}>
                  <div className="grid gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="name">Organization Name</Label>
                      <Input
                        id="name"
                        name="name"
                        defaultValue={organization.name}
                        required
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="slug">Organization Slug</Label>
                      <Input
                        id="slug"
                        name="slug"
                        value={organization.slug}
                        disabled
                      />
                      <p className="text-xs text-muted-foreground">
                        The slug is used in URLs and cannot be changed.
                      </p>
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="created">Created</Label>
                      <Input
                        id="created"
                        name="created"
                        value={organization.created_at ? formatDate(organization.created_at) : 'Unknown'}
                        disabled
                      />
                    </div>
                    <Button type="submit" className="mt-4" disabled={isUpdating}>
                      {isUpdating ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        'Save Changes'
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>

            {/* Danger Zone Card */}
            <Card className="border-destructive">
              <CardHeader>
                <CardTitle className="text-destructive">Danger Zone</CardTitle>
                <CardDescription>
                  Irreversible actions that affect your organization
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Delete Organization</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Permanently delete this organization and all of its data. This action cannot be undone.
                  </p>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="destructive">
                        Delete Organization
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Delete Organization</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to delete this organization? This action cannot be undone.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="py-4">
                        <p className="mb-4">
                          Please type <strong>{organization.name}</strong> to confirm.
                        </p>
                        <Input
                          id="confirmName"
                          placeholder={organization.name}
                          onChange={(e) => {
                            const deleteButton = document.getElementById('confirmDeleteButton') as HTMLButtonElement;
                            if (deleteButton) {
                              deleteButton.disabled = e.target.value !== organization.name;
                            }
                          }}
                        />
                      </div>
                      <DialogFooter>
                        <Button
                          id="confirmDeleteButton"
                          variant="destructive"
                          disabled={true}
                          onClick={() => {
                            // Here you would call the delete organization API
                            // For now, just navigate back to organizations list
                            router.push('/organizations');
                          }}
                        >
                          Delete Permanently
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
