import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getSession } from 'next-auth/react';

// Use the frontend URL for API calls - this will go through the frontend proxy
const FRONTEND_URL = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3300';

/**
 * Base API configuration for RTK Query
 * This sets up the base URL to use the frontend proxy instead of calling backend directly
 */
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: `${FRONTEND_URL}/api`,
    prepareHeaders: async (headers) => {
      // Note: Authentication is handled by the frontend proxy using getServerSession
      // We don't need to add Authorization headers here since the proxy handles it

      // Set content type for JSON requests
      if (!headers.get('Content-Type')) {
        headers.set('Content-Type', 'application/json');
      }

      return headers;
    },
  }),
  // Global configuration for all endpoints
  tagTypes: [
    'User',
    'Organization',
    'Project',
    'Translation',
    'Locale',
    'OrganizationMember',
    'PaymentMethod',
    'Subscription',
    'ApiKey',
    'PermissionGroup',
    'AuditLog'
  ],
  endpoints: () => ({}),
});

// Export hooks for usage in functional components
export const {
  util: { getRunningQueriesThunk },
} = apiSlice;
