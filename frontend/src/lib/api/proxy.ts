import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Backend API URL - using the correct port 8300
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8300';

interface ProxyOptions {
  requireAuth?: boolean;
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
  logRequests?: boolean;
}

// Default options
const DEFAULT_OPTIONS: ProxyOptions = {
  requireAuth: true,
  timeout: 30000, // 30 seconds
  retries: 1,
  logRequests: process.env.NODE_ENV === 'development',
};

/**
 * Utility function to log requests in development
 */
function logRequest(method: string, url: string, status?: number, duration?: number) {
  if (process.env.NODE_ENV === 'development') {
    const timestamp = new Date().toISOString();
    const statusText = status ? `[${status}]` : '[PENDING]';
    const durationText = duration ? `(${duration}ms)` : '';
    console.log(`[API PROXY] ${timestamp} ${statusText} ${method} ${url} ${durationText}`);
  }
}

/**
 * Generic proxy handler for forwarding requests to the backend API
 * This handles authentication, error handling, and response formatting
 */
export async function proxyRequest(
  request: NextRequest,
  backendPath: string,
  options: ProxyOptions = {}
) {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const startTime = Date.now();

  try {
    // Log the incoming request
    if (opts.logRequests) {
      logRequest(request.method, backendPath);
    }

    // Get the session if authentication is required
    let authHeader: Record<string, string> = {};

    if (opts.requireAuth) {
      const session = await getServerSession(authOptions);

      if (!session?.accessToken) {
        const duration = Date.now() - startTime;
        if (opts.logRequests) {
          logRequest(request.method, backendPath, 401, duration);
        }
        return NextResponse.json(
          {
            success: false,
            message: 'Unauthorized - No valid session found',
            data: null,
          },
          { status: 401 }
        );
      }

      authHeader = { 'Authorization': `Bearer ${session.accessToken}` };
    }
    
    // Build the backend URL
    const url = new URL(backendPath, BACKEND_API_URL);

    // Copy query parameters from the original request
    const searchParams = request.nextUrl.searchParams;
    searchParams.forEach((value, key) => {
      url.searchParams.append(key, value);
    });

    // Prepare headers - handle different content types
    const contentType = request.headers.get('content-type');
    const headers: Record<string, string> = {
      ...authHeader,
      ...opts.headers,
    };

    // Only set Content-Type if not already set and not multipart/form-data
    if (!headers['Content-Type'] && !contentType?.includes('multipart/form-data')) {
      headers['Content-Type'] = 'application/json';
    }

    // Forward important headers from the original request
    const headersToForward = ['user-agent', 'accept', 'accept-language', 'x-forwarded-for'];
    headersToForward.forEach(headerName => {
      const value = request.headers.get(headerName);
      if (value && !headers[headerName]) {
        headers[headerName] = value;
      }
    });

    // Prepare the request options
    const fetchOptions: RequestInit = {
      method: request.method,
      headers,
      signal: AbortSignal.timeout(opts.timeout!),
    };

    // Add body for non-GET requests
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      try {
        if (contentType?.includes('multipart/form-data')) {
          // Handle file uploads - pass FormData directly
          const formData = await request.formData();
          fetchOptions.body = formData;
          // Remove Content-Type header to let fetch set it with boundary
          delete headers['Content-Type'];
        } else if (contentType?.includes('application/json')) {
          // Handle JSON data
          const body = await request.json();
          fetchOptions.body = JSON.stringify(body);
        } else {
          // Handle other data types (text, etc.)
          const body = await request.text();
          if (body) {
            fetchOptions.body = body;
          }
        }
      } catch (error) {
        console.error('Error processing request body:', error);
        const duration = Date.now() - startTime;
        if (opts.logRequests) {
          logRequest(request.method, backendPath, 400, duration);
        }
        return NextResponse.json(
          {
            success: false,
            message: 'Invalid request body',
            data: null,
          },
          { status: 400 }
        );
      }
    }
    
    // Make the request to the backend with retry logic
    let response: Response;
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= opts.retries!; attempt++) {
      try {
        response = await fetch(url.toString(), fetchOptions);
        break;
      } catch (error) {
        lastError = error as Error;
        if (attempt === opts.retries) {
          throw error;
        }
        // Wait before retrying (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }

    const duration = Date.now() - startTime;

    // Log the response
    if (opts.logRequests) {
      logRequest(request.method, backendPath, response!.status, duration);
    }

    // Get the response data
    let data;
    const responseContentType = response!.headers.get('content-type');

    if (responseContentType?.includes('application/json')) {
      try {
        data = await response!.json();
      } catch {
        // If JSON parsing fails, treat as text
        data = await response!.text();
      }
    } else if (responseContentType?.includes('text/')) {
      data = await response!.text();
    } else {
      // For binary data, return as blob
      const blob = await response!.blob();
      return new NextResponse(blob, {
        status: response!.status,
        headers: {
          'Content-Type': responseContentType || 'application/octet-stream',
          'Content-Length': blob.size.toString(),
        },
      });
    }

    // Create the response with the same status code as the backend
    const nextResponse = NextResponse.json(data, { status: response!.status });

    // Copy relevant headers from backend response
    const headersToForward = [
      'x-total-count', 'x-page', 'x-per-page', 'x-total-pages',
      'cache-control', 'etag', 'last-modified', 'content-disposition'
    ];
    headersToForward.forEach(header => {
      const value = response!.headers.get(header);
      if (value) {
        nextResponse.headers.set(header, value);
      }
    });

    return nextResponse;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error('Proxy error:', {
      method: request.method,
      path: backendPath,
      error: error instanceof Error ? error.message : String(error),
      duration,
    });

    if (opts.logRequests) {
      logRequest(request.method, backendPath, 500, duration);
    }

    // Handle specific error types
    if (error instanceof Error) {
      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        return NextResponse.json(
          {
            success: false,
            message: 'Request timeout - backend service took too long to respond',
            data: null,
          },
          { status: 504 }
        );
      }

      if (error.message.includes('fetch') || error.message.includes('ECONNREFUSED')) {
        return NextResponse.json(
          {
            success: false,
            message: 'Unable to connect to backend service - please check if the backend is running',
            data: null,
          },
          { status: 503 }
        );
      }
    }

    return NextResponse.json(
      {
        success: false,
        message: 'An unexpected error occurred while processing the request',
        data: null,
        error: process.env.NODE_ENV === 'development' ? String(error) : undefined,
      },
      { status: 500 }
    );
  }
}

/**
 * Helper function to create route handlers
 */
export function createRouteHandlers(
  backendPath: string | ((request: NextRequest) => string),
  options: ProxyOptions = {}
) {
  const getPath = (request: NextRequest) => {
    if (typeof backendPath === 'function') {
      return backendPath(request);
    }
    return backendPath;
  };

  return {
    GET: (request: NextRequest) => proxyRequest(request, getPath(request), options),
    POST: (request: NextRequest) => proxyRequest(request, getPath(request), options),
    PUT: (request: NextRequest) => proxyRequest(request, getPath(request), options),
    DELETE: (request: NextRequest) => proxyRequest(request, getPath(request), options),
    PATCH: (request: NextRequest) => proxyRequest(request, getPath(request), options),
    OPTIONS: async (request: NextRequest) => {
      // Handle CORS preflight requests
      return new NextResponse(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
          'Access-Control-Max-Age': '86400',
        },
      });
    },
  };
}

/**
 * Helper function to create public route handlers (no authentication required)
 */
export function createPublicRouteHandlers(
  backendPath: string | ((request: NextRequest) => string),
  options: Omit<ProxyOptions, 'requireAuth'> = {}
) {
  return createRouteHandlers(backendPath, { ...options, requireAuth: false });
}

/**
 * Helper function to create file upload route handlers
 */
export function createUploadRouteHandlers(
  backendPath: string | ((request: NextRequest) => string),
  options: ProxyOptions = {}
) {
  return createRouteHandlers(backendPath, {
    ...options,
    timeout: 60000, // 60 seconds for file uploads
    headers: {
      // Don't set Content-Type for uploads, let the browser set it
      ...options.headers,
    },
  });
}
