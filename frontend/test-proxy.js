#!/usr/bin/env node

/**
 * Test script for the Universal API Proxy
 * Run this script to test if the proxy is working correctly
 * 
 * Usage: node test-proxy.js
 */

const http = require('http');

const FRONTEND_URL = 'http://localhost:3300';
const BACKEND_URL = 'http://localhost:8300';

// Test endpoints
const TEST_ENDPOINTS = [
  {
    name: 'Health Check (Public)',
    path: '/api/locales',
    method: 'GET',
    requiresAuth: false,
    expectedStatus: 200,
  },
  {
    name: 'User Profile (Protected)',
    path: '/api/users/me',
    method: 'GET',
    requiresAuth: true,
    expectedStatus: 401, // Should be 401 without auth
  },
  {
    name: 'Organizations (Protected)',
    path: '/api/organizations',
    method: 'GET',
    requiresAuth: true,
    expectedStatus: 401, // Should be 401 without auth
  },
  {
    name: 'Projects (Protected)',
    path: '/api/projects',
    method: 'GET',
    requiresAuth: true,
    expectedStatus: 401, // Should be 401 without auth
  },
];

/**
 * Make HTTP request
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = http.request(url, options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data,
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

/**
 * Test if service is running
 */
async function testService(url, name) {
  try {
    await makeRequest(url);
    console.log(`✅ ${name} is running`);
    return true;
  } catch (error) {
    console.log(`❌ ${name} is not running: ${error.message}`);
    return false;
  }
}

/**
 * Test an endpoint
 */
async function testEndpoint(endpoint) {
  const url = `${FRONTEND_URL}${endpoint.path}`;
  
  try {
    const response = await makeRequest(url, {
      method: endpoint.method,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const success = response.statusCode === endpoint.expectedStatus;
    const status = success ? '✅' : '❌';
    
    console.log(`${status} ${endpoint.name}`);
    console.log(`   Path: ${endpoint.path}`);
    console.log(`   Expected: ${endpoint.expectedStatus}, Got: ${response.statusCode}`);
    
    if (!success) {
      console.log(`   Response: ${response.data.substring(0, 200)}...`);
    }
    
    return success;
  } catch (error) {
    console.log(`❌ ${endpoint.name}`);
    console.log(`   Path: ${endpoint.path}`);
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🧪 Testing Universal API Proxy\n');
  
  // Test if services are running
  console.log('📡 Checking Services:');
  const frontendRunning = await testService(FRONTEND_URL, 'Frontend (Next.js)');
  const backendRunning = await testService(BACKEND_URL, 'Backend (Go)');
  
  if (!frontendRunning) {
    console.log('\n❌ Frontend is not running. Start it with: make frontend');
    process.exit(1);
  }
  
  if (!backendRunning) {
    console.log('\n⚠️  Backend is not running. Some tests may fail.');
    console.log('   Start it with: make backend');
  }
  
  console.log('\n🔍 Testing API Endpoints:');
  
  let passed = 0;
  let total = TEST_ENDPOINTS.length;
  
  for (const endpoint of TEST_ENDPOINTS) {
    const success = await testEndpoint(endpoint);
    if (success) passed++;
    console.log(''); // Empty line for readability
  }
  
  // Summary
  console.log('📊 Test Summary:');
  console.log(`   Passed: ${passed}/${total}`);
  console.log(`   Success Rate: ${Math.round((passed / total) * 100)}%`);
  
  if (passed === total) {
    console.log('\n🎉 All tests passed! Universal API Proxy is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the logs above for details.');
  }
  
  console.log('\n💡 Tips:');
  console.log('   - Make sure both frontend and backend are running');
  console.log('   - Check the browser console for detailed error messages');
  console.log('   - Use "make dev" to start both services');
  console.log('   - Use "make status" to check service status');
}

// Run the tests
runTests().catch((error) => {
  console.error('Test runner error:', error);
  process.exit(1);
});
